#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Network Monitor Simple - مراقب الشبكة المبسط
نسخة مبسطة لا تتطلب مكتبات خارجية
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import threading
import time
import subprocess
import platform
import socket
import os
import csv
from datetime import datetime

class SimpleNetworkMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.devices = {}
        self.data_file = "network_devices.json"
        self.monitoring = False
        self.language = "ar"
        self.total_package_gb = 140
        
        # Language dictionaries
        self.translations = {
            "ar": {
                "title": "مراقب الشبكة المبسط - Simple Network Monitor",
                "device_name": "اسم الجهاز",
                "mac_address": "عنوان MAC",
                "ip_address": "عنوان IP",
                "status": "الحالة",
                "last_seen": "آخر ظهور",
                "download_limit": "حد التحميل (KB/s)",
                "upload_limit": "حد الرفع (KB/s)",
                "block_internet": "قطع الإنترنت",
                "start_monitoring": "بدء المراقبة",
                "stop_monitoring": "إيقاف المراقبة",
                "save_data": "حفظ البيانات",
                "load_data": "تحميل البيانات",
                "language": "اللغة",
                "package_size": "حجم الباقة (GB)",
                "refresh_rate": "معدل التحديث (ثانية)",
                "export_csv": "تصدير CSV",
                "scan_network": "فحص الشبكة",
                "online": "متصل",
                "offline": "غير متصل",
                "edit_device": "تعديل الجهاز",
                "device_count": "عدد الأجهزة: {}"
            },
            "en": {
                "title": "Simple Network Monitor - مراقب الشبكة المبسط",
                "device_name": "Device Name",
                "mac_address": "MAC Address",
                "ip_address": "IP Address",
                "status": "Status",
                "last_seen": "Last Seen",
                "download_limit": "Download Limit (KB/s)",
                "upload_limit": "Upload Limit (KB/s)",
                "block_internet": "Block Internet",
                "start_monitoring": "Start Monitoring",
                "stop_monitoring": "Stop Monitoring",
                "save_data": "Save Data",
                "load_data": "Load Data",
                "language": "Language",
                "package_size": "Package Size (GB)",
                "refresh_rate": "Refresh Rate (seconds)",
                "export_csv": "Export CSV",
                "scan_network": "Scan Network",
                "online": "Online",
                "offline": "Offline",
                "edit_device": "Edit Device",
                "device_count": "Device Count: {}"
            }
        }
        
        self.setup_ui()
        self.load_data()
        
    def setup_window(self):
        self.root.title("Simple Network Monitor - مراقب الشبكة المبسط")
        self.root.geometry("1200x700")
        self.root.configure(bg='#f0f0f0')
        
        # Make window resizable
        self.root.rowconfigure(2, weight=1)
        self.root.columnconfigure(0, weight=1)
        
    def get_text(self, key):
        return self.translations[self.language].get(key, key)
        
    def setup_ui(self):
        # Clear existing widgets
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # Title
        title_label = tk.Label(self.root, text=self.get_text("title"), 
                              font=("Arial", 16, "bold"), bg='#f0f0f0')
        title_label.grid(row=0, column=0, pady=10)
        
        # Control Panel
        control_frame = ttk.Frame(self.root)
        control_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=5)
        
        # Language selection
        ttk.Label(control_frame, text=self.get_text("language")).grid(row=0, column=0, padx=5)
        self.language_var = tk.StringVar(value=self.language)
        language_combo = ttk.Combobox(control_frame, textvariable=self.language_var, 
                                    values=["ar", "en"], width=5, state="readonly")
        language_combo.grid(row=0, column=1, padx=5)
        language_combo.bind("<<ComboboxSelected>>", self.change_language)
        
        # Package size
        ttk.Label(control_frame, text=self.get_text("package_size")).grid(row=0, column=2, padx=5)
        self.package_var = tk.StringVar(value=str(self.total_package_gb))
        package_entry = ttk.Entry(control_frame, textvariable=self.package_var, width=10)
        package_entry.grid(row=0, column=3, padx=5)
        
        # Refresh rate
        ttk.Label(control_frame, text=self.get_text("refresh_rate")).grid(row=0, column=4, padx=5)
        self.refresh_var = tk.StringVar(value="10")
        refresh_entry = ttk.Entry(control_frame, textvariable=self.refresh_var, width=5)
        refresh_entry.grid(row=0, column=5, padx=5)
        
        # Control buttons
        button_frame = ttk.Frame(self.root)
        button_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=5)
        
        self.start_btn = ttk.Button(button_frame, text=self.get_text("start_monitoring"), 
                                   command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(button_frame, text=self.get_text("stop_monitoring"), 
                                  command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text=self.get_text("scan_network"), 
                  command=self.manual_scan).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text=self.get_text("save_data"), 
                  command=self.save_data).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text=self.get_text("load_data"), 
                  command=self.load_data).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text=self.get_text("export_csv"), 
                  command=self.export_csv).pack(side=tk.LEFT, padx=5)
        
        # Device count label
        self.count_label = ttk.Label(self.root, text="", font=("Arial", 10))
        self.count_label.grid(row=3, column=0, pady=5)
        
        # Device table
        self.setup_table()
        
    def setup_table(self):
        # Table frame
        table_frame = ttk.Frame(self.root)
        table_frame.grid(row=4, column=0, sticky="nsew", padx=10, pady=5)
        table_frame.rowconfigure(0, weight=1)
        table_frame.columnconfigure(0, weight=1)
        
        # Treeview with scrollbars
        columns = ("device_name", "mac_address", "ip_address", "status", "last_seen", 
                  "download_limit", "upload_limit", "block_internet")
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # Configure column headings and widths
        column_widths = {
            "device_name": 150,
            "mac_address": 140,
            "ip_address": 120,
            "status": 80,
            "last_seen": 120,
            "download_limit": 120,
            "upload_limit": 120,
            "block_internet": 100
        }
        
        for col in columns:
            self.tree.heading(col, text=self.get_text(col))
            self.tree.column(col, width=column_widths.get(col, 100), anchor="center")
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Bind double-click for editing
        self.tree.bind("<Double-1>", self.edit_device)
        
    def change_language(self, event=None):
        self.language = self.language_var.get()
        self.setup_ui()
        self.update_table()
        
    def get_local_ip(self):
        """Get local IP address"""
        try:
            # Connect to a remote address to determine local IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "***********"
            
    def get_network_range(self):
        """Get network range for scanning"""
        local_ip = self.get_local_ip()
        # Assume /24 network
        ip_parts = local_ip.split('.')
        network_base = '.'.join(ip_parts[:3])
        return network_base
        
    def ping_host(self, ip):
        """Ping a host to check if it's alive"""
        try:
            if platform.system().lower() == "windows":
                result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                                      capture_output=True, text=True, timeout=3)
            else:
                result = subprocess.run(['ping', '-c', '1', '-W', '1', ip], 
                                      capture_output=True, text=True, timeout=3)
            return result.returncode == 0
        except:
            return False
            
    def get_mac_address(self, ip):
        """Get MAC address for an IP using ARP"""
        try:
            if platform.system().lower() == "windows":
                result = subprocess.run(['arp', '-a', ip], capture_output=True, text=True)
                lines = result.stdout.split('\n')
                for line in lines:
                    if ip in line and ('dynamic' in line.lower() or 'static' in line.lower()):
                        parts = line.split()
                        if len(parts) >= 2:
                            mac = parts[1].strip().upper()
                            if self.is_valid_mac(mac):
                                return mac
            else:
                result = subprocess.run(['arp', '-n', ip], capture_output=True, text=True)
                lines = result.stdout.split('\n')
                for line in lines:
                    if ip in line:
                        parts = line.split()
                        if len(parts) >= 3:
                            mac = parts[2].strip().upper()
                            if self.is_valid_mac(mac):
                                return mac
        except:
            pass
        return None
        
    def is_valid_mac(self, mac):
        """Validate MAC address format"""
        try:
            if '-' in mac:
                parts = mac.split('-')
            elif ':' in mac:
                parts = mac.split(':')
            else:
                return False
            return len(parts) == 6 and all(len(part) == 2 for part in parts)
        except:
            return False

    def scan_network(self):
        """Scan network for devices"""
        network_base = self.get_network_range()
        found_devices = {}

        print(f"Scanning network {network_base}.1-254...")

        # Scan common IP range
        for i in range(1, 255):
            if not self.monitoring:
                break

            ip = f"{network_base}.{i}"

            if self.ping_host(ip):
                mac = self.get_mac_address(ip)
                if mac:
                    found_devices[mac] = {
                        'ip': ip,
                        'mac': mac,
                        'last_seen': time.time(),
                        'status': 'online'
                    }
                    print(f"Found device: {ip} - {mac}")
                else:
                    # Create a fake MAC for devices without ARP entry
                    fake_mac = f"00:00:00:00:{i:02X}:00"
                    found_devices[fake_mac] = {
                        'ip': ip,
                        'mac': fake_mac,
                        'last_seen': time.time(),
                        'status': 'online'
                    }
                    print(f"Found device: {ip} - No MAC")

        return found_devices

    def manual_scan(self):
        """Manual network scan"""
        def scan_thread():
            self.start_btn.config(state=tk.DISABLED)
            found = self.scan_network()

            # Update devices
            for mac, device_info in found.items():
                if mac not in self.devices:
                    self.devices[mac] = {
                        'name': f"Device_{device_info['ip'].split('.')[-1]}",
                        'ip': device_info['ip'],
                        'mac': mac,
                        'status': 'online',
                        'last_seen': time.time(),
                        'download_limit': 0,
                        'upload_limit': 0,
                        'blocked': False
                    }
                else:
                    self.devices[mac]['ip'] = device_info['ip']
                    self.devices[mac]['status'] = 'online'
                    self.devices[mac]['last_seen'] = time.time()

            # Mark offline devices
            current_time = time.time()
            for mac, device in self.devices.items():
                if mac not in found:
                    if current_time - device.get('last_seen', 0) > 300:  # 5 minutes
                        device['status'] = 'offline'

            self.root.after(0, self.update_table)
            self.start_btn.config(state=tk.NORMAL)

        threading.Thread(target=scan_thread, daemon=True).start()

    def monitor_network(self):
        """Continuous network monitoring"""
        while self.monitoring:
            try:
                found = self.scan_network()

                # Update devices
                for mac, device_info in found.items():
                    if mac not in self.devices:
                        self.devices[mac] = {
                            'name': f"Device_{device_info['ip'].split('.')[-1]}",
                            'ip': device_info['ip'],
                            'mac': mac,
                            'status': 'online',
                            'last_seen': time.time(),
                            'download_limit': 0,
                            'upload_limit': 0,
                            'blocked': False
                        }
                    else:
                        self.devices[mac]['ip'] = device_info['ip']
                        self.devices[mac]['status'] = 'online'
                        self.devices[mac]['last_seen'] = time.time()

                # Mark offline devices
                current_time = time.time()
                for mac, device in self.devices.items():
                    if mac not in found:
                        if current_time - device.get('last_seen', 0) > 300:  # 5 minutes
                            device['status'] = 'offline'

                # Update UI
                self.root.after(0, self.update_table)

                # Wait for next scan
                refresh_rate = int(self.refresh_var.get()) if self.refresh_var.get().isdigit() else 10
                time.sleep(refresh_rate)

            except Exception as e:
                print(f"Error in monitoring: {e}")
                time.sleep(10)

    def update_table(self):
        """Update the device table"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Add devices
        online_count = 0
        for mac, device in self.devices.items():
            if device.get('status') == 'online':
                online_count += 1

            last_seen = device.get('last_seen', 0)
            if last_seen > 0:
                last_seen_str = time.strftime('%H:%M:%S', time.localtime(last_seen))
            else:
                last_seen_str = "Never"

            status_text = self.get_text(device.get('status', 'offline'))

            values = (
                device.get('name', ''),
                mac,
                device.get('ip', ''),
                status_text,
                last_seen_str,
                device.get('download_limit', 0),
                device.get('upload_limit', 0),
                "✓" if device.get('blocked', False) else "✗"
            )

            # Color coding
            if device.get('status') == 'online':
                tags = ('online',)
            else:
                tags = ('offline',)

            self.tree.insert("", "end", values=values, tags=tags)

        # Configure tag colors
        self.tree.tag_configure('online', background='#e8f5e8')
        self.tree.tag_configure('offline', background='#f5e8e8')

        # Update count
        total_count = len(self.devices)
        count_text = self.get_text("device_count").format(f"{online_count}/{total_count}")
        self.count_label.config(text=count_text)

    def edit_device(self, event):
        """Edit device properties"""
        selection = self.tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.tree.item(item)['values']
        if not values:
            return

        mac = values[1]  # MAC address is in column 1

        if mac not in self.devices:
            return

        # Create edit window
        edit_window = tk.Toplevel(self.root)
        edit_window.title(f"{self.get_text('edit_device')} - {mac}")
        edit_window.geometry("400x350")
        edit_window.transient(self.root)
        edit_window.grab_set()

        device = self.devices[mac]

        # Device name
        ttk.Label(edit_window, text=self.get_text("device_name")).grid(row=0, column=0, padx=5, pady=5, sticky="w")
        name_var = tk.StringVar(value=device.get('name', ''))
        ttk.Entry(edit_window, textvariable=name_var, width=30).grid(row=0, column=1, padx=5, pady=5)

        # Download limit
        ttk.Label(edit_window, text=self.get_text("download_limit")).grid(row=1, column=0, padx=5, pady=5, sticky="w")
        dl_limit_var = tk.StringVar(value=str(device.get('download_limit', 0)))
        ttk.Entry(edit_window, textvariable=dl_limit_var, width=30).grid(row=1, column=1, padx=5, pady=5)

        # Upload limit
        ttk.Label(edit_window, text=self.get_text("upload_limit")).grid(row=2, column=0, padx=5, pady=5, sticky="w")
        ul_limit_var = tk.StringVar(value=str(device.get('upload_limit', 0)))
        ttk.Entry(edit_window, textvariable=ul_limit_var, width=30).grid(row=2, column=1, padx=5, pady=5)

        # Block internet
        block_var = tk.BooleanVar(value=device.get('blocked', False))
        ttk.Checkbutton(edit_window, text=self.get_text("block_internet"),
                       variable=block_var).grid(row=3, column=0, columnspan=2, padx=5, pady=5)

        # Device info (read-only)
        info_frame = ttk.LabelFrame(edit_window, text="Device Info")
        info_frame.grid(row=4, column=0, columnspan=2, padx=5, pady=10, sticky="ew")

        ttk.Label(info_frame, text=f"MAC: {mac}").grid(row=0, column=0, padx=5, pady=2, sticky="w")
        ttk.Label(info_frame, text=f"IP: {device.get('ip', 'Unknown')}").grid(row=1, column=0, padx=5, pady=2, sticky="w")
        ttk.Label(info_frame, text=f"Status: {device.get('status', 'Unknown')}").grid(row=2, column=0, padx=5, pady=2, sticky="w")

        def save_changes():
            device['name'] = name_var.get()
            try:
                device['download_limit'] = float(dl_limit_var.get())
                device['upload_limit'] = float(ul_limit_var.get())
            except ValueError:
                messagebox.showerror("Error", "Invalid limit values")
                return

            device['blocked'] = block_var.get()

            # Apply blocking if needed (simplified)
            if device['blocked']:
                print(f"Blocking device {mac} ({device.get('ip')})")
                # In a real implementation, you would add firewall rules here
            else:
                print(f"Unblocking device {mac} ({device.get('ip')})")

            edit_window.destroy()
            self.update_table()

        # Buttons
        button_frame = ttk.Frame(edit_window)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)

        ttk.Button(button_frame, text="Save", command=save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=edit_window.destroy).pack(side=tk.LEFT, padx=5)

    def start_monitoring(self):
        """Start network monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)

            # Start monitoring thread
            self.monitor_thread = threading.Thread(target=self.monitor_network, daemon=True)
            self.monitor_thread.start()

            print("Network monitoring started")

    def stop_monitoring(self):
        """Stop network monitoring"""
        if self.monitoring:
            self.monitoring = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)

            print("Network monitoring stopped")

    def save_data(self):
        """Save device data to file"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                data = {
                    'devices': self.devices,
                    'total_package_gb': self.total_package_gb,
                    'language': self.language,
                    'last_saved': datetime.now().isoformat()
                }
                json.dump(data, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("Success", f"Data saved to {self.data_file}")
            print(f"Data saved to {self.data_file}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save data: {e}")
            print(f"Error saving data: {e}")

    def load_data(self):
        """Load device data from file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.devices = data.get('devices', {})
                self.total_package_gb = data.get('total_package_gb', 140)
                self.language = data.get('language', 'ar')

                # Update UI
                self.package_var.set(str(self.total_package_gb))
                self.language_var.set(self.language)
                self.update_table()

                print(f"Data loaded from {self.data_file}")
            else:
                print(f"No data file found: {self.data_file}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load data: {e}")
            print(f"Error loading data: {e}")

    def export_csv(self):
        """Export device data to CSV"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Export to CSV"
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['Device Name', 'MAC Address', 'IP Address',
                                'Status', 'Last Seen', 'Download Limit',
                                'Upload Limit', 'Blocked']

                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for mac, device in self.devices.items():
                        last_seen = device.get('last_seen', 0)
                        if last_seen > 0:
                            last_seen_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_seen))
                        else:
                            last_seen_str = "Never"

                        writer.writerow({
                            'Device Name': device.get('name', ''),
                            'MAC Address': mac,
                            'IP Address': device.get('ip', ''),
                            'Status': device.get('status', 'Unknown'),
                            'Last Seen': last_seen_str,
                            'Download Limit': device.get('download_limit', 0),
                            'Upload Limit': device.get('upload_limit', 0),
                            'Blocked': "Yes" if device.get('blocked', False) else "No"
                        })

                messagebox.showinfo("Success", f"Data exported to {filename}")
                print(f"Data exported to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export data: {e}")
            print(f"Error exporting data: {e}")

    def run(self):
        """Run the application"""
        # Load data on startup
        self.load_data()

        # Handle window closing
        def on_closing():
            if self.monitoring:
                self.stop_monitoring()
            self.save_data()
            self.root.destroy()

        self.root.protocol("WM_DELETE_WINDOW", on_closing)

        # Start the GUI
        self.root.mainloop()

if __name__ == "__main__":
    try:
        print("Starting Simple Network Monitor...")
        print("This version doesn't require external libraries")
        print("Features:")
        print("- Network device discovery")
        print("- Device management")
        print("- Data export")
        print("- Bilingual interface (Arabic/English)")
        print()

        app = SimpleNetworkMonitor()
        app.run()
    except KeyboardInterrupt:
        print("\nApplication terminated by user")
    except Exception as e:
        print(f"Application error: {e}")
        import traceback
        traceback.print_exc()
