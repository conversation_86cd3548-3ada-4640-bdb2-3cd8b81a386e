#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Network Monitor - مراقب الشبكة
برنامج لمراقبة الأجهزة المتصلة بالشبكة وإدارة استهلاك البيانات
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import threading
import time
import subprocess
import platform
import socket
import struct
import psutil
import netifaces
from datetime import datetime
import os
import csv

class NetworkMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.devices = {}
        self.data_file = "network_devices.json"
        self.monitoring = False
        self.language = "ar"  # Default Arabic
        self.total_package_gb = 140  # Default package size
        
        # Language dictionaries
        self.translations = {
            "ar": {
                "title": "مراقب الشبكة - Network Monitor",
                "device_name": "اسم الجهاز",
                "mac_address": "عنوان MAC",
                "ip_address": "عنوان IP",
                "download_speed": "سرعة التحميل (KB/s)",
                "upload_speed": "سرعة الرفع (KB/s)",
                "download_mb": "التحميل (MB)",
                "upload_mb": "الرفع (MB)",
                "total_mb": "الإجمالي (MB)",
                "download_limit": "حد التحميل (KB/s)",
                "upload_limit": "حد الرفع (KB/s)",
                "block_internet": "قطع الإنترنت",
                "start_monitoring": "بدء المراقبة",
                "stop_monitoring": "إيقاف المراقبة",
                "reset_usage": "تصفير الاستهلاك",
                "save_data": "حفظ البيانات",
                "load_data": "تحميل البيانات",
                "language": "اللغة",
                "package_size": "حجم الباقة (GB)",
                "total_usage": "إجمالي الاستهلاك: {:.2f} GB من {} GB",
                "refresh_rate": "معدل التحديث (ثانية)",
                "export_csv": "تصدير CSV"
            },
            "en": {
                "title": "Network Monitor - مراقب الشبكة",
                "device_name": "Device Name",
                "mac_address": "MAC Address",
                "ip_address": "IP Address",
                "download_speed": "Download Speed (KB/s)",
                "upload_speed": "Upload Speed (KB/s)",
                "download_mb": "Download (MB)",
                "upload_mb": "Upload (MB)",
                "total_mb": "Total (MB)",
                "download_limit": "Download Limit (KB/s)",
                "upload_limit": "Upload Limit (KB/s)",
                "block_internet": "Block Internet",
                "start_monitoring": "Start Monitoring",
                "stop_monitoring": "Stop Monitoring",
                "reset_usage": "Reset Usage",
                "save_data": "Save Data",
                "load_data": "Load Data",
                "language": "Language",
                "package_size": "Package Size (GB)",
                "total_usage": "Total Usage: {:.2f} GB of {} GB",
                "refresh_rate": "Refresh Rate (seconds)",
                "export_csv": "Export CSV"
            }
        }
        
        self.setup_ui()
        self.load_data()
        
    def setup_window(self):
        self.root.title("Network Monitor - مراقب الشبكة")
        self.root.geometry("1400x800")
        self.root.configure(bg='#f0f0f0')
        
        # Make window resizable
        self.root.rowconfigure(1, weight=1)
        self.root.columnconfigure(0, weight=1)
        
    def get_text(self, key):
        return self.translations[self.language].get(key, key)
        
    def setup_ui(self):
        # Clear existing widgets
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # Control Panel
        control_frame = ttk.Frame(self.root)
        control_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)
        control_frame.columnconfigure(1, weight=1)
        
        # Language selection
        ttk.Label(control_frame, text=self.get_text("language")).grid(row=0, column=0, padx=5)
        self.language_var = tk.StringVar(value=self.language)
        language_combo = ttk.Combobox(control_frame, textvariable=self.language_var, 
                                    values=["ar", "en"], width=5, state="readonly")
        language_combo.grid(row=0, column=1, padx=5, sticky="w")
        language_combo.bind("<<ComboboxSelected>>", self.change_language)
        
        # Package size
        ttk.Label(control_frame, text=self.get_text("package_size")).grid(row=0, column=2, padx=5)
        self.package_var = tk.StringVar(value=str(self.total_package_gb))
        package_entry = ttk.Entry(control_frame, textvariable=self.package_var, width=10)
        package_entry.grid(row=0, column=3, padx=5)
        package_entry.bind("<Return>", self.update_package_size)
        
        # Refresh rate
        ttk.Label(control_frame, text=self.get_text("refresh_rate")).grid(row=0, column=4, padx=5)
        self.refresh_var = tk.StringVar(value="5")
        refresh_entry = ttk.Entry(control_frame, textvariable=self.refresh_var, width=5)
        refresh_entry.grid(row=0, column=5, padx=5)
        
        # Control buttons
        button_frame = ttk.Frame(self.root)
        button_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=5)
        
        self.start_btn = ttk.Button(button_frame, text=self.get_text("start_monitoring"), 
                                   command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(button_frame, text=self.get_text("stop_monitoring"), 
                                  command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text=self.get_text("reset_usage"), 
                  command=self.reset_usage).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text=self.get_text("save_data"), 
                  command=self.save_data).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text=self.get_text("load_data"), 
                  command=self.load_data).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text=self.get_text("export_csv"), 
                  command=self.export_csv).pack(side=tk.LEFT, padx=5)
        
        # Total usage label
        self.usage_label = ttk.Label(self.root, text="", font=("Arial", 12, "bold"))
        self.usage_label.grid(row=2, column=0, pady=5)
        
        # Device table
        self.setup_table()
        
    def setup_table(self):
        # Table frame
        table_frame = ttk.Frame(self.root)
        table_frame.grid(row=3, column=0, sticky="nsew", padx=10, pady=5)
        table_frame.rowconfigure(0, weight=1)
        table_frame.columnconfigure(0, weight=1)
        
        # Treeview with scrollbars
        columns = ("device_name", "mac_address", "ip_address", "download_speed", 
                  "upload_speed", "download_mb", "upload_mb", "total_mb", 
                  "download_limit", "upload_limit", "block_internet")
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # Configure column headings
        for col in columns:
            self.tree.heading(col, text=self.get_text(col))
            self.tree.column(col, width=120, anchor="center")
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Bind double-click for editing
        self.tree.bind("<Double-1>", self.edit_device)
        
    def change_language(self, event=None):
        self.language = self.language_var.get()
        self.setup_ui()
        self.update_table()
        
    def update_package_size(self, event=None):
        try:
            self.total_package_gb = float(self.package_var.get())
            self.update_usage_display()
        except ValueError:
            messagebox.showerror("Error", "Invalid package size")
            
    def get_network_interfaces(self):
        """Get all network interfaces"""
        interfaces = []
        for interface in netifaces.interfaces():
            if interface != 'lo':  # Skip loopback
                interfaces.append(interface)
        return interfaces
        
    def get_network_range(self):
        """Get the network range for scanning"""
        try:
            # Get default gateway
            gateways = netifaces.gateways()
            default_gateway = gateways['default'][netifaces.AF_INET][0]
            
            # Get network interface info
            for interface in netifaces.interfaces():
                addrs = netifaces.ifaddresses(interface)
                if netifaces.AF_INET in addrs:
                    for addr_info in addrs[netifaces.AF_INET]:
                        ip = addr_info['addr']
                        netmask = addr_info.get('netmask', '*************')
                        
                        # Check if this interface can reach the gateway
                        if self.is_same_network(ip, default_gateway, netmask):
                            return self.get_network_address(ip, netmask)
            
            # Fallback to common network ranges
            return "***********/24"
            
        except Exception as e:
            print(f"Error getting network range: {e}")
            return "***********/24"
            
    def is_same_network(self, ip1, ip2, netmask):
        """Check if two IPs are in the same network"""
        try:
            ip1_int = struct.unpack("!I", socket.inet_aton(ip1))[0]
            ip2_int = struct.unpack("!I", socket.inet_aton(ip2))[0]
            netmask_int = struct.unpack("!I", socket.inet_aton(netmask))[0]
            
            return (ip1_int & netmask_int) == (ip2_int & netmask_int)
        except:
            return False
            
    def get_network_address(self, ip, netmask):
        """Get network address from IP and netmask"""
        try:
            ip_int = struct.unpack("!I", socket.inet_aton(ip))[0]
            netmask_int = struct.unpack("!I", socket.inet_aton(netmask))[0]
            network_int = ip_int & netmask_int
            
            network_ip = socket.inet_ntoa(struct.pack("!I", network_int))
            
            # Calculate CIDR
            cidr = bin(netmask_int).count('1')
            
            return f"{network_ip}/{cidr}"
        except:
            return "***********/24"

    def scan_network(self):
        """Scan network for connected devices"""
        devices = {}

        try:
            # Use ARP table to find devices
            if platform.system() == "Windows":
                result = subprocess.run(['arp', '-a'], capture_output=True, text=True)
                lines = result.stdout.split('\n')

                for line in lines:
                    if 'dynamic' in line.lower() or 'static' in line.lower():
                        parts = line.split()
                        if len(parts) >= 3:
                            ip = parts[0].strip()
                            mac = parts[1].strip().upper()

                            if self.is_valid_ip(ip) and self.is_valid_mac(mac):
                                devices[mac] = {
                                    'ip': ip,
                                    'mac': mac,
                                    'last_seen': time.time()
                                }
            else:
                # Linux/Mac
                result = subprocess.run(['arp', '-a'], capture_output=True, text=True)
                lines = result.stdout.split('\n')

                for line in lines:
                    if '(' in line and ')' in line:
                        parts = line.split()
                        if len(parts) >= 4:
                            ip = parts[1].strip('()')
                            mac = parts[3].strip().upper()

                            if self.is_valid_ip(ip) and self.is_valid_mac(mac):
                                devices[mac] = {
                                    'ip': ip,
                                    'mac': mac,
                                    'last_seen': time.time()
                                }

        except Exception as e:
            print(f"Error scanning network: {e}")

        return devices

    def is_valid_ip(self, ip):
        """Validate IP address"""
        try:
            parts = ip.split('.')
            return len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts)
        except:
            return False

    def is_valid_mac(self, mac):
        """Validate MAC address"""
        try:
            parts = mac.split('-') if '-' in mac else mac.split(':')
            return len(parts) == 6 and all(len(part) == 2 for part in parts)
        except:
            return False

    def get_network_stats(self, interface=None):
        """Get network statistics"""
        stats = psutil.net_io_counters(pernic=True)

        total_bytes_sent = 0
        total_bytes_recv = 0

        if interface:
            if interface in stats:
                return stats[interface].bytes_sent, stats[interface].bytes_recv
        else:
            for iface, stat in stats.items():
                if iface != 'lo':  # Skip loopback
                    total_bytes_sent += stat.bytes_sent
                    total_bytes_recv += stat.bytes_recv

        return total_bytes_sent, total_bytes_recv

    def calculate_speed(self, mac, current_sent, current_recv):
        """Calculate upload/download speed for a device"""
        current_time = time.time()

        if mac in self.devices:
            device = self.devices[mac]

            if 'last_sent' in device and 'last_recv' in device and 'last_time' in device:
                time_diff = current_time - device['last_time']

                if time_diff > 0:
                    sent_diff = current_sent - device['last_sent']
                    recv_diff = current_recv - device['last_recv']

                    upload_speed = (sent_diff / time_diff) / 1024  # KB/s
                    download_speed = (recv_diff / time_diff) / 1024  # KB/s

                    # Update totals
                    device['total_upload'] = device.get('total_upload', 0) + (sent_diff / (1024 * 1024))  # MB
                    device['total_download'] = device.get('total_download', 0) + (recv_diff / (1024 * 1024))  # MB

                    device['upload_speed'] = max(0, upload_speed)
                    device['download_speed'] = max(0, download_speed)
                else:
                    device['upload_speed'] = 0
                    device['download_speed'] = 0
            else:
                device['upload_speed'] = 0
                device['download_speed'] = 0
                device['total_upload'] = device.get('total_upload', 0)
                device['total_download'] = device.get('total_download', 0)

            device['last_sent'] = current_sent
            device['last_recv'] = current_recv
            device['last_time'] = current_time

    def monitor_network(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Scan for devices
                scanned_devices = self.scan_network()

                # Update device list
                for mac, device_info in scanned_devices.items():
                    if mac not in self.devices:
                        # New device
                        self.devices[mac] = {
                            'name': f"Device_{mac[-5:]}",
                            'ip': device_info['ip'],
                            'mac': mac,
                            'upload_speed': 0,
                            'download_speed': 0,
                            'total_upload': 0,
                            'total_download': 0,
                            'upload_limit': 0,
                            'download_limit': 0,
                            'blocked': False,
                            'last_seen': time.time()
                        }
                    else:
                        # Update existing device
                        self.devices[mac]['ip'] = device_info['ip']
                        self.devices[mac]['last_seen'] = time.time()

                # Calculate speeds (simplified - in real implementation you'd need per-device monitoring)
                for mac in self.devices:
                    # This is a simplified calculation
                    # In a real implementation, you'd need to monitor per-device traffic
                    sent, recv = self.get_network_stats()
                    self.calculate_speed(mac, sent, recv)

                # Update UI
                self.root.after(0, self.update_table)
                self.root.after(0, self.update_usage_display)

                # Wait for next scan
                refresh_rate = float(self.refresh_var.get()) if self.refresh_var.get().isdigit() else 5
                time.sleep(refresh_rate)

            except Exception as e:
                print(f"Error in monitoring: {e}")
                time.sleep(5)

    def update_table(self):
        """Update the device table"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Add devices
        for mac, device in self.devices.items():
            total_mb = device.get('total_download', 0) + device.get('total_upload', 0)

            values = (
                device.get('name', ''),
                mac,
                device.get('ip', ''),
                f"{device.get('download_speed', 0):.1f}",
                f"{device.get('upload_speed', 0):.1f}",
                f"{device.get('total_download', 0):.1f}",
                f"{device.get('total_upload', 0):.1f}",
                f"{total_mb:.1f}",
                device.get('download_limit', 0),
                device.get('upload_limit', 0),
                "✓" if device.get('blocked', False) else "✗"
            )

            self.tree.insert("", "end", values=values, tags=(mac,))

    def update_usage_display(self):
        """Update total usage display"""
        total_gb = sum(
            device.get('total_download', 0) + device.get('total_upload', 0)
            for device in self.devices.values()
        ) / 1024  # Convert MB to GB

        usage_text = self.get_text("total_usage").format(total_gb, self.total_package_gb)
        self.usage_label.config(text=usage_text)

    def edit_device(self, event):
        """Edit device properties"""
        selection = self.tree.selection()
        if not selection:
            return

        item = selection[0]
        mac = self.tree.item(item)['tags'][0]

        if mac not in self.devices:
            return

        # Create edit window
        edit_window = tk.Toplevel(self.root)
        edit_window.title(f"Edit Device - {mac}")
        edit_window.geometry("400x300")
        edit_window.transient(self.root)
        edit_window.grab_set()

        device = self.devices[mac]

        # Device name
        ttk.Label(edit_window, text=self.get_text("device_name")).grid(row=0, column=0, padx=5, pady=5, sticky="w")
        name_var = tk.StringVar(value=device.get('name', ''))
        ttk.Entry(edit_window, textvariable=name_var, width=30).grid(row=0, column=1, padx=5, pady=5)

        # Download limit
        ttk.Label(edit_window, text=self.get_text("download_limit")).grid(row=1, column=0, padx=5, pady=5, sticky="w")
        dl_limit_var = tk.StringVar(value=str(device.get('download_limit', 0)))
        ttk.Entry(edit_window, textvariable=dl_limit_var, width=30).grid(row=1, column=1, padx=5, pady=5)

        # Upload limit
        ttk.Label(edit_window, text=self.get_text("upload_limit")).grid(row=2, column=0, padx=5, pady=5, sticky="w")
        ul_limit_var = tk.StringVar(value=str(device.get('upload_limit', 0)))
        ttk.Entry(edit_window, textvariable=ul_limit_var, width=30).grid(row=2, column=1, padx=5, pady=5)

        # Block internet
        block_var = tk.BooleanVar(value=device.get('blocked', False))
        ttk.Checkbutton(edit_window, text=self.get_text("block_internet"),
                       variable=block_var).grid(row=3, column=0, columnspan=2, padx=5, pady=5)

        def save_changes():
            device['name'] = name_var.get()
            try:
                device['download_limit'] = float(dl_limit_var.get())
                device['upload_limit'] = float(ul_limit_var.get())
            except ValueError:
                messagebox.showerror("Error", "Invalid limit values")
                return

            device['blocked'] = block_var.get()

            # Apply blocking if needed
            if device['blocked']:
                self.block_device(mac)
            else:
                self.unblock_device(mac)

            edit_window.destroy()
            self.update_table()

        # Buttons
        button_frame = ttk.Frame(edit_window)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)

        ttk.Button(button_frame, text="Save", command=save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=edit_window.destroy).pack(side=tk.LEFT, padx=5)

    def block_device(self, mac):
        """Block internet access for a device"""
        try:
            device = self.devices.get(mac)
            if not device:
                return

            ip = device.get('ip')
            if not ip:
                return

            # Note: This requires administrative privileges
            if platform.system() == "Windows":
                # Windows firewall rule
                rule_name = f"NetworkMonitor_Block_{mac.replace(':', '_')}"
                cmd = f'netsh advfirewall firewall add rule name="{rule_name}" dir=out action=block remoteip={ip}'
                subprocess.run(cmd, shell=True, capture_output=True)

                cmd = f'netsh advfirewall firewall add rule name="{rule_name}_in" dir=in action=block remoteip={ip}'
                subprocess.run(cmd, shell=True, capture_output=True)
            else:
                # Linux iptables
                subprocess.run(['sudo', 'iptables', '-A', 'FORWARD', '-s', ip, '-j', 'DROP'],
                             capture_output=True)
                subprocess.run(['sudo', 'iptables', '-A', 'FORWARD', '-d', ip, '-j', 'DROP'],
                             capture_output=True)

            print(f"Blocked device {mac} ({ip})")

        except Exception as e:
            print(f"Error blocking device {mac}: {e}")
            messagebox.showerror("Error", f"Failed to block device. Run as administrator.\nError: {e}")

    def unblock_device(self, mac):
        """Unblock internet access for a device"""
        try:
            device = self.devices.get(mac)
            if not device:
                return

            ip = device.get('ip')
            if not ip:
                return

            if platform.system() == "Windows":
                # Remove Windows firewall rules
                rule_name = f"NetworkMonitor_Block_{mac.replace(':', '_')}"
                cmd = f'netsh advfirewall firewall delete rule name="{rule_name}"'
                subprocess.run(cmd, shell=True, capture_output=True)

                cmd = f'netsh advfirewall firewall delete rule name="{rule_name}_in"'
                subprocess.run(cmd, shell=True, capture_output=True)
            else:
                # Remove Linux iptables rules
                subprocess.run(['sudo', 'iptables', '-D', 'FORWARD', '-s', ip, '-j', 'DROP'],
                             capture_output=True)
                subprocess.run(['sudo', 'iptables', '-D', 'FORWARD', '-d', ip, '-j', 'DROP'],
                             capture_output=True)

            print(f"Unblocked device {mac} ({ip})")

        except Exception as e:
            print(f"Error unblocking device {mac}: {e}")

    def start_monitoring(self):
        """Start network monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)

            # Start monitoring thread
            self.monitor_thread = threading.Thread(target=self.monitor_network, daemon=True)
            self.monitor_thread.start()

            print("Network monitoring started")

    def stop_monitoring(self):
        """Stop network monitoring"""
        if self.monitoring:
            self.monitoring = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)

            print("Network monitoring stopped")

    def reset_usage(self):
        """Reset usage statistics for all devices"""
        if messagebox.askyesno("Confirm", "Reset usage statistics for all devices?"):
            for device in self.devices.values():
                device['total_download'] = 0
                device['total_upload'] = 0

            self.update_table()
            self.update_usage_display()
            print("Usage statistics reset")

    def save_data(self):
        """Save device data to file"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                # Prepare data for JSON serialization
                data = {
                    'devices': self.devices,
                    'total_package_gb': self.total_package_gb,
                    'language': self.language,
                    'last_saved': datetime.now().isoformat()
                }
                json.dump(data, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("Success", f"Data saved to {self.data_file}")
            print(f"Data saved to {self.data_file}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save data: {e}")
            print(f"Error saving data: {e}")

    def load_data(self):
        """Load device data from file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.devices = data.get('devices', {})
                self.total_package_gb = data.get('total_package_gb', 140)
                self.language = data.get('language', 'ar')

                # Update UI
                self.package_var.set(str(self.total_package_gb))
                self.language_var.set(self.language)
                self.update_table()
                self.update_usage_display()

                print(f"Data loaded from {self.data_file}")
            else:
                print(f"No data file found: {self.data_file}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load data: {e}")
            print(f"Error loading data: {e}")

    def export_csv(self):
        """Export device data to CSV"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Export to CSV"
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['Device Name', 'MAC Address', 'IP Address',
                                'Download Speed (KB/s)', 'Upload Speed (KB/s)',
                                'Download (MB)', 'Upload (MB)', 'Total (MB)',
                                'Download Limit', 'Upload Limit', 'Blocked']

                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for mac, device in self.devices.items():
                        total_mb = device.get('total_download', 0) + device.get('total_upload', 0)

                        writer.writerow({
                            'Device Name': device.get('name', ''),
                            'MAC Address': mac,
                            'IP Address': device.get('ip', ''),
                            'Download Speed (KB/s)': f"{device.get('download_speed', 0):.1f}",
                            'Upload Speed (KB/s)': f"{device.get('upload_speed', 0):.1f}",
                            'Download (MB)': f"{device.get('total_download', 0):.1f}",
                            'Upload (MB)': f"{device.get('total_upload', 0):.1f}",
                            'Total (MB)': f"{total_mb:.1f}",
                            'Download Limit': device.get('download_limit', 0),
                            'Upload Limit': device.get('upload_limit', 0),
                            'Blocked': "Yes" if device.get('blocked', False) else "No"
                        })

                messagebox.showinfo("Success", f"Data exported to {filename}")
                print(f"Data exported to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export data: {e}")
            print(f"Error exporting data: {e}")

    def run(self):
        """Run the application"""
        # Load data on startup
        self.load_data()

        # Handle window closing
        def on_closing():
            if self.monitoring:
                self.stop_monitoring()
            self.save_data()
            self.root.destroy()

        self.root.protocol("WM_DELETE_WINDOW", on_closing)

        # Start the GUI
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = NetworkMonitor()
        app.run()
    except KeyboardInterrupt:
        print("\nApplication terminated by user")
    except Exception as e:
        print(f"Application error: {e}")
        import traceback
        traceback.print_exc()
