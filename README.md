# Network Monitor - مراق<PERSON> الشبكة

برنامج Python بواجهة رسومية لمراقبة الأجهزة المتصلة بالشبكة وإدارة استهلاك البيانات.

## المميزات

- **مراقبة الأجهزة**: عرض جميع الأجهزة المتصلة بالشبكة (Wi-Fi و LAN)
- **معلومات مفصلة**: MAC Address, IP Address, أسماء مخصصة للأجهزة
- **مراقبة السرعة**: سرعة التحميل والرفع الحالية بالكيلوبت
- **إحصائيات الاستهلاك**: حجم البيانات المحمّلة والمرفوعة بالميجابايت
- **حدود السرعة**: تحديد الحد الأقصى للتحميل والرفع لكل جهاز
- **قطع الإنترنت**: إمكانية قطع الإنترنت عن أجهزة محددة
- **حفظ البيانات**: حفظ جميع الإعدادات والإحصائيات في ملف JSON
- **تصدير البيانات**: تصدير البيانات إلى ملف CSV
- **دعم اللغات**: العربية والإنجليزية
- **واجهة أنيقة**: واجهة رسومية سهلة الاستخدام

## متطلبات النظام

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, Linux, macOS
- صلاحيات المدير (لقطع الإنترنت)

## التثبيت

1. **تثبيت Python**: تأكد من تثبيت Python 3.7 أو أحدث
2. **تثبيت المكتبات المطلوبة**:
```bash
pip install -r requirements.txt
```

أو تثبيت المكتبات يدوياً:
```bash
pip install psutil netifaces scapy
```

## التشغيل

### تشغيل عادي:
```bash
python network_monitor.py
```

### تشغيل بصلاحيات المدير (مطلوب لقطع الإنترنت):

**Windows:**
```bash
# تشغيل Command Prompt كمدير ثم:
python network_monitor.py
```

**Linux/macOS:**
```bash
sudo python3 network_monitor.py
```

## كيفية الاستخدام

### 1. بدء المراقبة
- اضغط على زر "بدء المراقبة" لبدء فحص الشبكة
- سيتم تحديث قائمة الأجهزة كل 5 ثوانٍ (قابل للتعديل)

### 2. تخصيص الأجهزة
- اضغط مرتين على أي جهاز في الجدول لتعديل إعداداته
- يمكنك تغيير:
  - اسم الجهاز
  - حد سرعة التحميل
  - حد سرعة الرفع
  - قطع/تشغيل الإنترنت

### 3. إدارة البيانات
- **حفظ البيانات**: حفظ جميع الإعدادات والإحصائيات
- **تحميل البيانات**: استرجاع البيانات المحفوظة
- **تصفير الاستهلاك**: إعادة تصفير عدادات الاستهلاك
- **تصدير CSV**: تصدير البيانات إلى ملف Excel

### 4. الإعدادات
- **حجم الباقة**: تحديد حجم باقة الإنترنت بالجيجابايت
- **معدل التحديث**: تغيير فترة تحديث البيانات
- **اللغة**: التبديل بين العربية والإنجليزية

## ملاحظات مهمة

### قطع الإنترنت
لتفعيل ميزة قطع الإنترنت، يجب تشغيل البرنامج بصلاحيات المدير:

**Windows:**
- يستخدم Windows Firewall لإنشاء قواعد حظر
- يتطلب تشغيل Command Prompt كمدير

**Linux:**
- يستخدم iptables لحظر الترافيك
- يتطلب صلاحيات sudo

### التحكم في السرعة
البرنامج الحالي يعرض حدود السرعة ولكن لا يطبقها تلقائياً. لتطبيق حدود السرعة:

**Windows:**
```bash
# استخدام NetLimiter أو برامج مشابهة
# أو استخدام QoS في الراوتر
```

**Linux:**
```bash
# استخدام tc (traffic control)
sudo tc qdisc add dev eth0 root handle 1: htb default 30
sudo tc class add dev eth0 parent 1: classid 1:1 htb rate 1000kbit
```

### دقة القياسات
- قياس السرعة تقريبي ويعتمد على إحصائيات النظام العامة
- للحصول على قياسات دقيقة لكل جهاز، يُنصح باستخدام:
  - مراقبة الراوتر مباشرة
  - أدوات متخصصة مثل PRTG أو Cacti

## استكشاف الأخطاء

### مشاكل شائعة:

1. **لا تظهر الأجهزة**:
   - تأكد من الاتصال بنفس الشبكة
   - جرب تشغيل البرنامج كمدير
   - تحقق من إعدادات الجدار الناري

2. **لا يعمل قطع الإنترنت**:
   - تأكد من تشغيل البرنامج بصلاحيات المدير
   - تحقق من إعدادات Windows Firewall أو iptables

3. **قياسات السرعة غير دقيقة**:
   - هذا طبيعي لأن البرنامج يستخدم إحصائيات عامة
   - للدقة الأكبر، استخدم مراقبة الراوتر

## الملفات المُنشأة

- `network_devices.json`: ملف حفظ البيانات والإعدادات
- `exported_data.csv`: ملف تصدير البيانات (عند التصدير)

## المساهمة

يمكنك المساهمة في تطوير البرنامج عبر:
- إضافة ميزات جديدة
- تحسين دقة القياسات
- إضافة دعم لأنظمة تشغيل أخرى
- تحسين الواجهة الرسومية

## الترخيص

هذا البرنامج مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## إخلاء المسؤولية

- استخدم البرنامج على مسؤوليتك الخاصة
- تأكد من امتلاك الصلاحيات اللازمة لمراقبة الشبكة
- لا تستخدم البرنامج لأغراض غير قانونية
