# دليل البدء السريع - Quick Start Guide

## النسخة المبسطة (بدون مكتبات خارجية)

### التشغيل السريع:
```bash
python network_monitor_simple.py
```

### المميزات المتوفرة:
✅ **اكتشاف الأجهزة**: فحص الشبكة للعثور على الأجهزة المتصلة  
✅ **إدارة الأجهزة**: تسمية الأجهزة وتحديد حدود السرعة  
✅ **حفظ البيانات**: حفظ واسترجاع إعدادات الأجهزة  
✅ **تصدير البيانات**: تصدير إلى ملف CSV  
✅ **واجهة ثنائية اللغة**: العربية والإنجليزية  
✅ **مراقبة الحالة**: معرفة الأجهزة المتصلة/غير المتصلة  

### المميزات المحدودة:
⚠️ **قياس السرعة**: غير متوفر في النسخة المبسطة  
⚠️ **قطع الإنترنت**: يتطلب صلاحيات المدير  
⚠️ **التحكم في السرعة**: يتطلب أدوات إضافية  

---

## النسخة الكاملة (تتطلب مكتبات)

### تثبيت المكتبات:
```bash
pip install psutil netifaces scapy
```

### التشغيل:
```bash
python network_monitor.py
```

### المميزات الإضافية:
✅ **قياس السرعة الفعلي**: مراقبة سرعة التحميل والرفع  
✅ **إحصائيات الاستهلاك**: حساب البيانات المستهلكة  
✅ **قطع الإنترنت**: حظر الأجهزة من الوصول للإنترنت  

---

## كيفية الاستخدام

### 1. فحص الشبكة
- اضغط "فحص الشبكة" للبحث عن الأجهزة
- أو اضغط "بدء المراقبة" للمراقبة المستمرة

### 2. تخصيص الأجهزة
- اضغط مرتين على أي جهاز لتعديله
- غيّر الاسم، حدود السرعة، أو احظر الجهاز

### 3. حفظ البيانات
- البيانات تُحفظ تلقائياً عند إغلاق البرنامج
- يمكنك الحفظ يدوياً بالضغط على "حفظ البيانات"

### 4. تصدير التقارير
- اضغط "تصدير CSV" لحفظ تقرير بالأجهزة

---

## نصائح مهمة

### للحصول على أفضل النتائج:
1. **شغّل البرنامج كمدير** (Administrator) لتفعيل جميع المميزات
2. **تأكد من الاتصال بنفس الشبكة** التي تريد مراقبتها
3. **انتظر قليلاً** بعد بدء الفحص لظهور جميع الأجهزة

### استكشاف الأخطاء:
- **لا تظهر أجهزة**: جرب تشغيل البرنامج كمدير
- **فحص بطيء**: قلل معدل التحديث أو استخدم الفحص اليدوي
- **لا يعمل قطع الإنترنت**: تأكد من تشغيل البرنامج بصلاحيات المدير

---

## الملفات المُنشأة

- `network_devices.json`: بيانات الأجهزة والإعدادات
- `exported_data.csv`: ملف التصدير (عند الحاجة)

---

## للمطورين

### تخصيص البرنامج:
- عدّل ملف `config.json` لتغيير الإعدادات الافتراضية
- أضف ميزات جديدة في الكود
- استخدم المكتبات الإضافية للمميزات المتقدمة

### المكتبات المستخدمة:
- `tkinter`: الواجهة الرسومية
- `psutil`: إحصائيات النظام (النسخة الكاملة)
- `netifaces`: معلومات الشبكة (النسخة الكاملة)
- `scapy`: تحليل الشبكة (النسخة الكاملة)
