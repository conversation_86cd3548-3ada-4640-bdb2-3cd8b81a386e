# دليل النسخة الكاملة - Full Version Guide

## 🎉 النسخة الكاملة تعمل بنجاح!

تم تشغيل النسخة الكاملة من مراقب الشبكة بنجاح. هذه النسخة تحتوي على جميع المميزات المتقدمة.

---

## 🚀 التشغيل السريع

```bash
python network_monitor.py
```

---

## ✨ المميزات الإضافية في النسخة الكاملة

### 📊 **قياس السرعة الفعلي**:
- مراقبة سرعة التحميل والرفع لكل جهاز
- إحصائيات دقيقة للاستهلاك
- رسوم بيانية للسرعة (إذا كانت متوفرة)

### 🚫 **قطع الإنترنت**:
- حظر أجهزة معينة من الوصول للإنترنت
- **يتطلب تشغيل البرنامج كمدير**
- يعمل على Windows, Linux, macOS

### 📈 **إحصائيات متقدمة**:
- حساب البيانات المستهلكة بدقة
- مراقبة الاستهلاك الشهري
- تنبيهات عند تجاوز الحدود

### 🔧 **تحكم متقدم**:
- تحديد حدود السرعة (يتطلب أدوات إضافية)
- مراقبة أكثر دقة للشبكة
- معلومات تفصيلية عن كل جهاز

---

## 🛠️ كيفية الاستخدام المتقدم

### 1. **تشغيل البرنامج كمدير** (للحصول على جميع المميزات):

#### Windows:
- اضغط بالزر الأيمن على Command Prompt
- اختر "Run as administrator"
- انتقل إلى مجلد البرنامج
- شغّل: `python network_monitor.py`

#### Linux/macOS:
```bash
sudo python3 network_monitor.py
```

### 2. **استخدام ميزة قطع الإنترنت**:
- اضغط مرتين على أي جهاز
- فعّل خيار "حظر الجهاز"
- احفظ التغييرات
- **ملاحظة**: يتطلب صلاحيات المدير

### 3. **مراقبة السرعة**:
- ابدأ المراقبة المستمرة
- راقب عمود "سرعة التحميل" و "سرعة الرفع"
- البيانات تُحدث كل 5-10 ثوانٍ

### 4. **إعداد حدود السرعة**:
- اضغط مرتين على الجهاز
- اضبط "حد التحميل" و "حد الرفع"
- **ملاحظة**: قد يتطلب أدوات إضافية للتطبيق الفعلي

---

## 📋 الواجهة الرئيسية

### الأعمدة المتوفرة:
1. **اسم الجهاز**: اسم مخصص أو تلقائي
2. **عنوان MAC**: المعرف الفريد للجهاز
3. **عنوان IP**: العنوان الحالي في الشبكة
4. **الحالة**: متصل/غير متصل
5. **آخر ظهور**: آخر مرة تم رصد الجهاز
6. **سرعة التحميل**: السرعة الحالية (KB/s)
7. **سرعة الرفع**: السرعة الحالية (KB/s)
8. **الاستهلاك**: إجمالي البيانات المستهلكة
9. **حد التحميل**: الحد الأقصى المسموح
10. **حد الرفع**: الحد الأقصى المسموح
11. **محظور**: حالة الحظر

### الأزرار المتوفرة:
- **فحص الشبكة**: فحص فوري للأجهزة
- **بدء المراقبة**: مراقبة مستمرة
- **إيقاف المراقبة**: إيقاف المراقبة
- **حفظ البيانات**: حفظ يدوي
- **تصدير CSV**: تصدير التقرير
- **تغيير اللغة**: التبديل بين العربية والإنجليزية

---

## ⚙️ الإعدادات المتقدمة

### تخصيص معدل التحديث:
- افتح ملف `config.json`
- غيّر قيمة `scan_interval` (بالثواني)
- أعد تشغيل البرنامج

### تخصيص نطاق الشبكة:
- افتح ملف `config.json`
- غيّر قيمة `network_range`
- مثال: `"192.168.0.1-254"` أو `"10.0.0.1-100"`

### تخصيص الواجهة:
- غيّر `window_title` لتخصيص عنوان النافذة
- غيّر `default_language` للغة الافتراضية
- غيّر `total_package_gb` للحد الشهري

---

## 🔍 استكشاف الأخطاء

### إذا لم تظهر السرعات:
1. تأكد من تشغيل البرنامج كمدير
2. تحقق من وجود حركة بيانات على الجهاز
3. انتظر بضع دقائق لجمع البيانات

### إذا لم يعمل قطع الإنترنت:
1. **تأكد من تشغيل البرنامج كمدير**
2. تحقق من إعدادات الجدار الناري
3. جرب إعادة تشغيل الراوتر

### إذا كان البرنامج بطيئاً:
1. زد فترة التحديث في الإعدادات
2. قلل نطاق الشبكة المراقب
3. أغلق البرامج الأخرى التي تستخدم الشبكة

---

## 📊 فهم البيانات

### سرعة التحميل/الرفع:
- تُقاس بـ KB/s (كيلوبايت في الثانية)
- تُحدث كل 5-10 ثوانٍ
- تعكس الاستخدام الفعلي للجهاز

### الاستهلاك:
- إجمالي البيانات منذ بدء المراقبة
- يُحفظ ويُسترجع تلقائياً
- يمكن إعادة تعيينه من إعدادات الجهاز

### حالة الاتصال:
- **متصل**: الجهاز يرد على ping
- **غير متصل**: لا يرد على ping
- **غير معروف**: لم يتم فحصه بعد

---

## 🎯 نصائح للاستخدام الأمثل

### للمراقبة الدقيقة:
1. شغّل البرنامج كمدير دائماً
2. اتركه يعمل لفترة لجمع بيانات كافية
3. استخدم المراقبة المستمرة بدلاً من الفحص اليدوي

### لتوفير الموارد:
1. زد فترة التحديث إلى 30-60 ثانية
2. قلل نطاق الشبكة إذا كان لديك أجهزة قليلة
3. استخدم الفحص اليدوي للشبكات الكبيرة

### للأمان:
1. لا تشارك ملف البيانات مع أشخاص غير موثوقين
2. استخدم كلمات مرور قوية للراوتر
3. راقب الأجهزة غير المعروفة بانتظام

---

## 🔄 التحديثات والصيانة

### حفظ البيانات:
- البيانات تُحفظ تلقائياً عند إغلاق البرنامج
- يمكن الحفظ اليدوي بالضغط على "حفظ البيانات"
- الملف: `network_devices.json`

### النسخ الاحتياطي:
- انسخ ملف `network_devices.json` بانتظام
- احفظ ملف `config.json` بعد التخصيص
- احتفظ بنسخة من البرنامج

### التحديث:
- تحقق من وجود إصدارات جديدة
- احفظ بياناتك قبل التحديث
- اقرأ ملاحظات الإصدار

---

## 🎉 استمتع بالمراقبة المتقدمة!

النسخة الكاملة تعطيك تحكماً كاملاً في شبكتك. استخدم المميزات بحكمة ولا تتردد في تجربة الإعدادات المختلفة!
