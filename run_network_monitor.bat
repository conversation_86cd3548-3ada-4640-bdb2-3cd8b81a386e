@echo off
chcp 65001 >nul
echo Network Monitor - مراقب الشبكة
echo ================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or newer from https://python.org
    pause
    exit /b 1
)

echo Choose version to run:
echo 1. Simple Version (No external libraries required)
echo 2. Full Version (Requires psutil, netifaces, scapy)
echo.
set /p choice="Enter your choice (1 or 2): "

if "%choice%"=="1" (
    echo Starting Simple Network Monitor...
    echo This version works without external libraries
    echo.
    python network_monitor_simple.py
    goto end
)

if "%choice%"=="2" (
    REM Check if required packages are installed
    echo Checking required packages...
    python -c "import psutil, netifaces" >nul 2>&1
    if errorlevel 1 (
        echo Installing required packages...
        pip install -r requirements.txt
        if errorlevel 1 (
            echo Error: Failed to install required packages
            echo Trying to install individually...
            pip install psutil
            pip install netifaces
            echo.
            echo Note: scapy installation might fail on some systems
            echo The program will work with basic functionality
        )
    )

    echo Starting Full Network Monitor...
    echo.
    echo Note: For full functionality (blocking devices), run as Administrator
    echo.
    python network_monitor.py
    goto end
)

echo Invalid choice. Starting Simple Version...
python network_monitor_simple.py

:end
if errorlevel 1 (
    echo.
    echo Error: Failed to start Network Monitor
    echo Check the error messages above
)
echo.
echo Press any key to exit...
pause >nul
