#!/bin/bash

echo "Network Monitor - مراقب الشبكة"
echo "================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3.7 or newer"
    exit 1
fi

# Check if required packages are installed
echo "Checking required packages..."
python3 -c "import psutil, netifaces" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing required packages..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install required packages"
        echo "Please run: pip3 install psutil netifaces scapy"
        exit 1
    fi
fi

echo "Starting Network Monitor..."
echo
echo "Note: For full functionality (blocking devices), run with sudo"
echo

# Check if running with sudo for blocking functionality
if [ "$EUID" -ne 0 ]; then
    echo "Warning: Not running as root. Device blocking will not work."
    echo "To enable device blocking, run: sudo ./run_network_monitor.sh"
    echo
fi

# Run the network monitor
python3 network_monitor.py

if [ $? -ne 0 ]; then
    echo
    echo "Error: Failed to start Network Monitor"
    echo "Check the error messages above"
    read -p "Press Enter to continue..."
fi
