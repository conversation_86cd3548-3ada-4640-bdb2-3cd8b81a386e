# المميزات المتقدمة - Advanced Features

## قطع الإنترنت عن الأجهزة

### Windows (يتطلب صلاحيات المدير)

#### تشغيل البرنامج كمدير:
1. اض<PERSON><PERSON> بالزر الأيمن على `run_network_monitor.bat`
2. اختر "Run as administrator"
3. أو افتح Command Prompt كمدير وشغّل البرنامج

#### قطع الإنترنت يدوياً:
```cmd
# قطع الإنترنت عن جهاز معين
netsh advfirewall firewall add rule name="Block_Device_*************" dir=out action=block remoteip=*************
netsh advfirewall firewall add rule name="Block_Device_*************_in" dir=in action=block remoteip=*************

# إلغاء القطع
netsh advfirewall firewall delete rule name="Block_Device_*************"
netsh advfirewall firewall delete rule name="Block_Device_*************_in"
```

### Linux/macOS (يتطلب sudo)

#### تشغيل البرنامج:
```bash
sudo python3 network_monitor.py
```

#### قطع الإنترنت يدوياً:
```bash
# قطع الإنترنت عن جهاز معين
sudo iptables -A FORWARD -s ************* -j DROP
sudo iptables -A FORWARD -d ************* -j DROP

# إلغاء القطع
sudo iptables -D FORWARD -s ************* -j DROP
sudo iptables -D FORWARD -d ************* -j DROP
```

---

## التحكم في سرعة الإنترنت

### Windows

#### استخدام NetLimiter (برنامج مدفوع):
1. حمّل NetLimiter من الموقع الرسمي
2. ثبّت البرنامج وشغّله كمدير
3. حدد الجهاز أو التطبيق
4. اضبط حدود السرعة

#### استخدام TMeter (مجاني):
1. حمّل TMeter من الموقع الرسمي
2. ثبّت البرنامج
3. اضبط قواعد التحكم في السرعة

#### استخدام Windows QoS:
```cmd
# تفعيل QoS
netsh int tcp set global autotuninglevel=normal

# إنشاء سياسة QoS (يتطلب Group Policy Editor)
# gpedit.msc > Computer Configuration > Windows Settings > Policy-based QoS
```

### Linux

#### استخدام tc (Traffic Control):
```bash
# إنشاء qdisc جذر
sudo tc qdisc add dev eth0 root handle 1: htb default 30

# إنشاء class للتحكم في السرعة
sudo tc class add dev eth0 parent 1: classid 1:1 htb rate 1000kbit

# تطبيق القاعدة على IP معين
sudo tc filter add dev eth0 protocol ip parent 1:0 prio 1 u32 match ip dst *************/32 flowid 1:1

# حذف القواعد
sudo tc qdisc del dev eth0 root
```

#### استخدام wondershaper:
```bash
# تثبيت wondershaper
sudo apt-get install wondershaper

# تحديد سرعة للواجهة
sudo wondershaper eth0 1024 512  # 1024 KB/s download, 512 KB/s upload

# إلغاء التحديد
sudo wondershaper clear eth0
```

### macOS

#### استخدام pfctl:
```bash
# إنشاء ملف قواعد
echo "dummynet-anchor \"bandwidth\"" | sudo tee -a /etc/pf.conf
echo "anchor \"bandwidth\"" | sudo tee -a /etc/pf.conf

# تطبيق قاعدة تحديد السرعة
echo "dummynet in quick on en0 from ************* to any pipe 1" | sudo pfctl -a bandwidth -f -
echo "dummynet out quick on en0 from any to ************* pipe 2" | sudo pfctl -a bandwidth -f -

# تحديد السرعة
sudo dnctl pipe 1 config bw 1Mbit/s
sudo dnctl pipe 2 config bw 512Kbit/s
```

---

## مراقبة الشبكة المتقدمة

### استخدام أدوات احترافية:

#### PRTG Network Monitor:
- مراقبة شاملة للشبكة
- واجهة ويب سهلة الاستخدام
- تقارير مفصلة

#### Cacti:
- مراقبة مفتوحة المصدر
- رسوم بيانية تفصيلية
- قابل للتخصيص

#### Nagios:
- مراقبة الخوادم والشبكات
- تنبيهات فورية
- مجتمع كبير من المطورين

### مراقبة الراوتر مباشرة:

#### SNMP:
```python
# مثال لقراءة إحصائيات SNMP
from pysnmp.hlapi import *

def get_interface_stats(router_ip, community='public'):
    for (errorIndication, errorStatus, errorIndex, varBinds) in nextCmd(
        SnmpEngine(),
        CommunityData(community),
        UdpTransportTarget((router_ip, 161)),
        ContextData(),
        ObjectType(ObjectIdentity('*******.*******.1.10')),  # ifInOctets
        lexicographicMode=False):
        
        if errorIndication:
            print(errorIndication)
            break
        elif errorStatus:
            print('%s at %s' % (errorStatus.prettyPrint(),
                                errorIndex and varBinds[int(errorIndex) - 1][0] or '?'))
            break
        else:
            for varBind in varBinds:
                print(' = '.join([x.prettyPrint() for x in varBind]))
```

---

## نصائح للأداء الأمثل

### تحسين دقة المراقبة:
1. **قلل فترة التحديث** للحصول على بيانات أكثر دقة
2. **استخدم مراقبة الراوتر** بدلاً من مراقبة الجهاز المحلي
3. **فعّل SNMP** على الراوتر للحصول على إحصائيات دقيقة

### تحسين الأداء:
1. **زد فترة التحديث** إذا كان البرنامج بطيئاً
2. **استخدم الفحص اليدوي** بدلاً من المراقبة المستمرة
3. **أغلق البرامج الأخرى** التي تستخدم الشبكة بكثافة

### الأمان:
1. **لا تشارك بيانات الشبكة** مع أشخاص غير موثوقين
2. **استخدم كلمات مرور قوية** للراوتر
3. **حدّث البرنامج** بانتظام للحصول على أحدث الميزات الأمنية

---

## استكشاف الأخطاء المتقدم

### مشاكل قطع الإنترنت:
- **تحقق من صلاحيات المدير**
- **تأكد من عدم وجود برامج حماية تمنع التعديل**
- **جرب إعادة تشغيل الراوتر**

### مشاكل قياس السرعة:
- **استخدم أدوات خارجية للتحقق** (مثل iperf)
- **تحقق من إعدادات الشبكة**
- **تأكد من عدم وجود برامج أخرى تستخدم الشبكة**

### مشاكل الاتصال:
- **تحقق من إعدادات الجدار الناري**
- **تأكد من الاتصال بنفس الشبكة**
- **جرب تشغيل البرنامج على جهاز آخر**
